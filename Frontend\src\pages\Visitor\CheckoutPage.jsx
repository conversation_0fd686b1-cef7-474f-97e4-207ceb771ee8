import React from "react";
import "../../styles/CheckoutPage.css";

const CheckoutPage = () => {
  return (
    <div className="checkout-page">
      {/* Main Content */}
      <div className="checkout-container">
        <div className="checkout-content">
          {/* Left Section - Checkout Form */}
          <div className="checkout-left">
            <div className="checkout-form-container">
              <h1 className="checkout-title">Checkout</h1>

              {/* Alert Message */}
              <div className="checkout-alert">
                Please log in to purchase the content!
              </div>

              {/* Sign In Section */}
              <div className="signin-section">
              <div className="signin-sectioncontainer">
                  <h2 className="signin-title">Sign In</h2>
                <p className="signin-subtitle">
                  Don't have an account? <a href="/signup" className="signup-link">Sign Up</a>
                </p>
              </div>

                <form className="signin-form">
                  <div className="form-group">
                    <input
                      type="email"
                      placeholder="Email address"
                      className="form-input"
                    />
                  </div>

                  <button type="submit" className="btn-primary">
                    Send Verification Code
                  </button>
                </form>
              </div>
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Your Order</h2>

              {/* Item Info */}
              <div className="item-info-section">
                <h3 className="item-info-title">Item Info</h3>

                <div className="item-details">
                  <div className="item-image">
                    <img
                      src="https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"
                      alt=""
                      className="item-thumbnail"
                    />
                  </div>

                  <div className="item-description">
                    <h4 className="item-name">
                      Frank Martin - Drills and Coaching Philosophy to Developing Toughness in Basketball
                    </h4>
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div className="pricing-section">
                <div className="price-row">
                  <span className="price-label">Subtotal</span>
                  <span className="price-value">$22.00</span>
                </div>

                <div className="price-row total-row">
                  <span className="price-label">Total</span>
                  <span className="price-value">$22.00</span>
                </div>
              </div>

              {/* Place Order Button */}
              <button className="place-order-btn btn-primary">
                Place Order & Download
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
